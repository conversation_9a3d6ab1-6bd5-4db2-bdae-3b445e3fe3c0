
import tkinter as tk
from tkinter import ttk, messagebox


import uuid
from datetime import datetime
from decimal import Decimal
from pathlib import Path
from typing import Dict, List, Optional, Any
import threading
import queue

# استيراد الأنظمة المطورة
try:
    from core.advanced_dependency_manager import AdvancedDependencyManager
    from database.advanced_items_database import AdvancedItemsDatabase
    from ai.intelligent_item_manager import IntelligentItemManager
    from analytics.advanced_analytics_engine import AdvancedAnalyticsEngine
except ImportError as e:
    print(f"تحذير: فشل في استيراد بعض الوحدات: {e}")

# محاولة استيراد مكتبات إضافية
try:
    import customtkinter as ctk
    CTK_AVAILABLE = True
except ImportError:
    CTK_AVAILABLE = False

try:
    from PIL import Image, ImageTk
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False


class AdvancedItemEntryComprehensive:
    """نافذة إدخال الأصناف الشاملة والمتقدمة"""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.project_root = Path(__file__).parent.parent
        
        # تهيئة الأنظمة
        self.dependency_manager = AdvancedDependencyManager()
        self.database = AdvancedItemsDatabase()
        self.ai_manager = IntelligentItemManager(self.database.get_connection())
        self.analytics_engine = AdvancedAnalyticsEngine(
            self.database.get_connection())
        
        # متغيرات البيانات
        self.current_item_data = {}
        self.categories_data = []
        self.units_data = []
        self.validation_queue = queue.Queue()
        
        # إعدادات الواجهة
        self.ui_config = {
            'theme': 'modern',
            'rtl_support': True,
            'font_family': 'Segoe UI',
            'font_size': 11,
            'colors': {
                'primary': '#2E86AB',
                'secondary': '#A23B72',
                'success': '#4CAF50',
                'warning': '#FF9800',
                'error': '#F44336',
                'background': '#F5F5F5',
                'surface': '#FFFFFF',
                'text': '#212121'
            }
        }
        
        # إنشاء النافذة الرئيسية
        self.create_main_window()
        
        # تحميل البيانات الأساسية
        self.load_initial_data()
        
        # تشغيل فحص التبعيات في الخلفية
        self.check_dependencies_async()
    
    def create_main_window(self):
        """إنشاء النافذة الرئيسية"""
        self.window = tk.Toplevel(self.parent) if self.parent else tk.Tk()
        self.window.title("نظام إدخال الأصناف المتقدم - الإصدار الشامل")
        self.window.geometry("1400x900")
        self.window.minsize(1200, 800)
        
        # تطبيق الثيم
        self.apply_theme()
        
        # إنشاء القوائم
        self.create_menu_bar()
        
        # إنشاء شريط الأدوات
        self.create_toolbar()
        
        # إنشاء المحتوى الرئيسي
        self.create_main_content()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
        
        # ربط الأحداث
        self.bind_events()
        
        # تطبيق اختصارات لوحة المفاتيح
        self.setup_keyboard_shortcuts()

        # تحميل البيانات الأولية
        self.load_items_list()
    
    def apply_theme(self):
        """تطبيق الثيم المحدد"""
        style = ttk.Style()
        
        if CTK_AVAILABLE and self.ui_config['theme'] == 'modern':
            # استخدام CustomTkinter للثيم الحديث
            ctk.set_appearance_mode("light")
            ctk.set_default_color_theme("blue")
        else:
            # استخدام ttk التقليدي
            try:
                style.theme_use('clam')
            except:
                style.theme_use('default')
        
        # تخصيص الألوان
        colors = self.ui_config['colors']
        style.configure('Title.TLabel', 
                       font=(self.ui_config['font_family'], 14, 'bold'),
                       foreground=colors['primary'])
        
        style.configure('Heading.TLabel',
                       font=(self.ui_config['font_family'], 12, 'bold'),
                       foreground=colors['text'])
        
        style.configure('Success.TLabel',
                       foreground=colors['success'])
        
        style.configure('Warning.TLabel',
                       foreground=colors['warning'])
        
        style.configure('Error.TLabel',
                       foreground=colors['error'])
    
    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        self.menubar = tk.Menu(self.window)
        self.window.config(menu=self.menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="جديد", command=self.new_item, accelerator="Ctrl+N")
        file_menu.add_command(label="حفظ", command=self.save_item,
                              accelerator="Ctrl+S")
        file_menu.add_separator()
        file_menu.add_command(label="استيراد", command=self.import_items)
        file_menu.add_command(label="تصدير", command=self.export_items)
        file_menu.add_separator()
        file_menu.add_command(label="إغلاق", command=self.close_window)
        
        # قائمة التحرير
        edit_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="تحرير", menu=edit_menu)
        edit_menu.add_command(label="تراجع", command=self.undo_action,
                              accelerator="Ctrl+Z")
        edit_menu.add_command(label="إعادة", command=self.redo_action,
                              accelerator="Ctrl+Y")
        edit_menu.add_separator()
        edit_menu.add_command(label="نسخ", command=self.copy_item,
                              accelerator="Ctrl+C")
        edit_menu.add_command(label="لصق", command=self.paste_item,
                              accelerator="Ctrl+V")
        
        # قائمة الذكاء الاصطناعي
        ai_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="ذكاء اصطناعي", menu=ai_menu)
        ai_menu.add_command(label="اقتراح التصنيف", command=self.suggest_category)
        ai_menu.add_command(label="توقع السعر", command=self.predict_price)
        ai_menu.add_command(label="كشف التكرار", command=self.detect_duplicates)
        ai_menu.add_command(label="تقييم الجودة", command=self.evaluate_quality)
        
        # قائمة التحليلات
        analytics_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="تحليلات", menu=analytics_menu)
        analytics_menu.add_command(label="اتجاه المبيعات",
                                   command=self.show_sales_trend)
        analytics_menu.add_command(label="توزيع الفئات",
                                   command=self.show_category_distribution)
        analytics_menu.add_command(label="تحليل الربحية",
                                   command=self.show_profit_analysis)
        analytics_menu.add_command(label="حالة المخزون",
                                   command=self.show_inventory_status)
        
        # قائمة المساعدة
        help_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="دليل المستخدم", command=self.show_user_guide)
        help_menu.add_command(label="اختصارات لوحة المفاتيح",
                              command=self.show_shortcuts)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        self.toolbar_frame = ttk.Frame(self.window)
        self.toolbar_frame.pack(fill=tk.X, padx=5, pady=2)
        
        # أزرار الأدوات الرئيسية
        ttk.Button(self.toolbar_frame, text="جديد",
                   command=self.new_item).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar_frame, text="حفظ",
                   command=self.save_item).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar_frame, text="حذف",
                   command=self.delete_item).pack(side=tk.LEFT, padx=2)

        ttk.Separator(self.toolbar_frame, orient=tk.VERTICAL).pack(
            side=tk.LEFT, padx=5, fill=tk.Y)
        
        # أزرار الذكاء الاصطناعي
        ttk.Button(self.toolbar_frame, text="🤖 اقتراح",
                   command=self.suggest_category).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar_frame, text="💰 توقع السعر",
                   command=self.predict_price).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar_frame, text="🔍 كشف التكرار",
                   command=self.detect_duplicates).pack(side=tk.LEFT, padx=2)

        ttk.Separator(self.toolbar_frame, orient=tk.VERTICAL).pack(
            side=tk.LEFT, padx=5, fill=tk.Y)
        
        # أزرار التحليلات
        ttk.Button(self.toolbar_frame, text="📊 تحليلات",
                   command=self.show_analytics_panel).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar_frame, text="📈 رسوم بيانية",
                   command=self.show_charts_panel).pack(side=tk.LEFT, padx=2)

        ttk.Separator(self.toolbar_frame, orient=tk.VERTICAL).pack(
            side=tk.LEFT, padx=5, fill=tk.Y)

        # أزرار إضافية
        ttk.Button(self.toolbar_frame, text="🔄 تحديث",
                   command=self.refresh_data).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar_frame, text="📝 بيانات تجريبية",
                   command=self.add_sample_data).pack(side=tk.LEFT, padx=2)
        
        # شريط البحث
        search_frame = ttk.Frame(self.toolbar_frame)
        search_frame.pack(side=tk.RIGHT, padx=5)
        
        ttk.Label(search_frame, text="بحث:").pack(side=tk.LEFT, padx=2)
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(search_frame,
                                      textvariable=self.search_var, width=20)
        self.search_entry.pack(side=tk.LEFT, padx=2)
        self.search_entry.bind('<KeyRelease>', self.on_search_change)

        ttk.Button(search_frame, text="🔍",
                   command=self.perform_search).pack(side=tk.LEFT, padx=2)
    
    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        # إنشاء PanedWindow للتقسيم
        self.main_paned = ttk.PanedWindow(self.window, orient=tk.HORIZONTAL)
        self.main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # الجانب الأيسر - نموذج الإدخال
        self.create_input_panel()
        
        # الجانب الأيمن - قائمة الأصناف والمعاينة
        self.create_preview_panel()
    
    def create_input_panel(self):
        """إنشاء لوحة الإدخال"""
        self.input_frame = ttk.Frame(self.main_paned)
        self.main_paned.add(self.input_frame, weight=2)
        
        # إنشاء Notebook للتبويبات
        self.notebook = ttk.Notebook(self.input_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # تبويب المعلومات الأساسية
        self.create_basic_info_tab()
        
        # تبويب التسعير والمخزون
        self.create_pricing_inventory_tab()
        
        # تبويب المعلومات الإضافية
        self.create_additional_info_tab()
        
        # تبويب الذكاء الاصطناعي
        self.create_ai_tab()
        
        # تبويب التحليلات
        self.create_analytics_tab()
    
    def create_basic_info_tab(self):
        """إنشاء تبويب المعلومات الأساسية"""
        self.basic_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.basic_frame, text="معلومات أساسية")
        
        # إنشاء Canvas للتمرير
        canvas = tk.Canvas(self.basic_frame)
        scrollbar = ttk.Scrollbar(self.basic_frame, orient="vertical",
                                  command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط العناصر
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # الحقول الأساسية
        row = 0
        
        # اسم الصنف
        ttk.Label(scrollable_frame, text="اسم الصنف *:",
                  style='Heading.TLabel').grid(
            row=row, column=0, sticky="w", padx=10, pady=5)
        self.name_var = tk.StringVar()
        self.name_entry = ttk.Entry(scrollable_frame,
                                    textvariable=self.name_var, width=40)
        self.name_entry.grid(row=row, column=1, columnspan=2,
                             sticky="ew", padx=10, pady=5)
        self.name_entry.bind('<FocusOut>', self.on_name_change)
        row += 1
        
        # رمز الصنف
        ttk.Label(scrollable_frame, text="رمز الصنف *:").grid(
            row=row, column=0, sticky="w", padx=10, pady=5)
        self.code_var = tk.StringVar()
        self.code_entry = ttk.Entry(scrollable_frame,
                                    textvariable=self.code_var, width=20)
        self.code_entry.grid(row=row, column=1, sticky="ew", padx=10, pady=5)

        # زر توليد رمز تلقائي
        ttk.Button(scrollable_frame, text="توليد تلقائي",
                   command=self.generate_auto_code).grid(
            row=row, column=2, padx=5, pady=5)
        row += 1
        
        # الفئة
        ttk.Label(scrollable_frame, text="الفئة *:").grid(
            row=row, column=0, sticky="w", padx=10, pady=5)
        self.category_var = tk.StringVar()
        self.category_combo = ttk.Combobox(
            scrollable_frame, textvariable=self.category_var,
            width=30, state="readonly")
        self.category_combo.grid(row=row, column=1, sticky="ew", padx=10, pady=5)

        # زر إضافة فئة جديدة
        ttk.Button(scrollable_frame, text="إضافة فئة",
                   command=self.add_new_category).grid(
            row=row, column=2, padx=5, pady=5)
        row += 1
        
        # الوحدة
        ttk.Label(scrollable_frame, text="وحدة القياس *:").grid(
            row=row, column=0, sticky="w", padx=10, pady=5)
        self.unit_var = tk.StringVar()
        self.unit_combo = ttk.Combobox(scrollable_frame, textvariable=self.unit_var, 
                                      width=20, state="readonly")
        self.unit_combo.grid(row=row, column=1, sticky="ew", padx=10, pady=5)
        
        # زر إضافة وحدة جديدة
        ttk.Button(scrollable_frame, text="إضافة وحدة", 
                  command=self.add_new_unit).grid(row=row, column=2, padx=5, pady=5)
        row += 1
        
        # الوصف
        ttk.Label(scrollable_frame, text="الوصف:").grid(
            row=row, column=0, sticky="nw", padx=10, pady=5)
        self.description_text = tk.Text(scrollable_frame, width=40, height=4)
        self.description_text.grid(row=row, column=1, columnspan=2,
                                   sticky="ew", padx=10, pady=5)
        row += 1

        # الباركود
        ttk.Label(scrollable_frame, text="الباركود:").grid(
            row=row, column=0, sticky="w", padx=10, pady=5)
        self.barcode_var = tk.StringVar()
        self.barcode_entry = ttk.Entry(scrollable_frame,
                                       textvariable=self.barcode_var, width=30)
        self.barcode_entry.grid(row=row, column=1, sticky="ew", padx=10, pady=5)

        # زر توليد باركود
        ttk.Button(scrollable_frame, text="توليد باركود",
                   command=self.generate_barcode).grid(
            row=row, column=2, padx=5, pady=5)
        row += 1
        
        # تكوين الأعمدة للتمدد
        scrollable_frame.columnconfigure(1, weight=1)
    
    def load_initial_data(self):
        """تحميل البيانات الأساسية"""
        try:
            # تحميل الفئات
            self.load_categories()
            
            # تحميل الوحدات
            self.load_units()
            
            # تحديث شريط الحالة
            self.update_status("تم تحميل البيانات الأساسية بنجاح")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل البيانات الأساسية: {e}")
    
    def load_categories(self):
        """تحميل الفئات من قاعدة البيانات"""
        try:
            conn = self.database.get_connection()
            cursor = conn.cursor()
            cursor.execute(
                "SELECT id, name FROM categories WHERE is_active = 1 ORDER BY name")
            self.categories_data = cursor.fetchall()

            # تحديث القائمة المنسدلة
            category_names = [''] + [row['name'] for row in self.categories_data]
            self.category_combo['values'] = category_names

        except Exception as e:
            print(f"خطأ في تحميل الفئات: {e}")
    
    def load_units(self):
        """تحميل الوحدات من قاعدة البيانات"""
        try:
            conn = self.database.get_connection()
            cursor = conn.cursor()
            cursor.execute(
                "SELECT id, name FROM units WHERE is_active = 1 ORDER BY name")
            self.units_data = cursor.fetchall()

            # تحديث القائمة المنسدلة
            unit_names = ['قطعة'] + [row['name'] for row in self.units_data]
            self.unit_combo['values'] = unit_names

        except Exception as e:
            print(f"خطأ في تحميل الوحدات: {e}")

    def create_pricing_inventory_tab(self):
        """إنشاء تبويب التسعير والمخزون"""
        self.pricing_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.pricing_frame, text="تسعير ومخزون")

        # إنشاء Canvas للتمرير
        canvas = tk.Canvas(self.pricing_frame)
        scrollbar = ttk.Scrollbar(self.pricing_frame, orient="vertical",
                                  command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        row = 0

        # قسم التسعير
        ttk.Label(scrollable_frame, text="معلومات التسعير",
                  style='Title.TLabel').grid(
            row=row, column=0, columnspan=3, sticky="w", padx=10, pady=10)
        row += 1

        # سعر التكلفة
        ttk.Label(scrollable_frame, text="سعر التكلفة *:").grid(
            row=row, column=0, sticky="w", padx=10, pady=5)
        self.cost_price_var = tk.StringVar()
        self.cost_price_entry = ttk.Entry(scrollable_frame,
                                          textvariable=self.cost_price_var, width=15)
        self.cost_price_entry.grid(row=row, column=1, sticky="w", padx=10, pady=5)
        self.cost_price_entry.bind('<FocusOut>', self.calculate_suggested_price)
        ttk.Label(scrollable_frame, text="ريال").grid(
            row=row, column=2, sticky="w", padx=5, pady=5)
        row += 1

        # سعر البيع
        ttk.Label(scrollable_frame, text="سعر البيع *:").grid(
            row=row, column=0, sticky="w", padx=10, pady=5)
        self.selling_price_var = tk.StringVar()
        self.selling_price_entry = ttk.Entry(scrollable_frame,
                                             textvariable=self.selling_price_var, width=15)
        self.selling_price_entry.grid(row=row, column=1, sticky="w", padx=10, pady=5)
        ttk.Label(scrollable_frame, text="ريال").grid(
            row=row, column=2, sticky="w", padx=5, pady=5)
        row += 1

        # هامش الربح
        ttk.Label(scrollable_frame, text="هامش الربح:").grid(
            row=row, column=0, sticky="w", padx=10, pady=5)
        self.profit_margin_label = ttk.Label(scrollable_frame, text="0%",
                                             style='Success.TLabel')
        self.profit_margin_label.grid(row=row, column=1, sticky="w", padx=10, pady=5)
        row += 1

        # الحد الأدنى والأقصى للسعر
        ttk.Label(scrollable_frame, text="الحد الأدنى للسعر:").grid(
            row=row, column=0, sticky="w", padx=10, pady=5)
        self.min_price_var = tk.StringVar()
        ttk.Entry(scrollable_frame, textvariable=self.min_price_var, width=15).grid(
            row=row, column=1, sticky="w", padx=10, pady=5)
        row += 1

        ttk.Label(scrollable_frame, text="الحد الأقصى للسعر:").grid(
            row=row, column=0, sticky="w", padx=10, pady=5)
        self.max_price_var = tk.StringVar()
        ttk.Entry(scrollable_frame, textvariable=self.max_price_var, width=15).grid(
            row=row, column=1, sticky="w", padx=10, pady=5)
        row += 1

        # فاصل
        ttk.Separator(scrollable_frame, orient='horizontal').grid(
            row=row, column=0, columnspan=3, sticky="ew", padx=10, pady=15)
        row += 1

        # قسم المخزون
        ttk.Label(scrollable_frame, text="معلومات المخزون",
                  style='Title.TLabel').grid(
            row=row, column=0, columnspan=3, sticky="w", padx=10, pady=10)
        row += 1

        # المخزون الحالي
        ttk.Label(scrollable_frame, text="المخزون الحالي:").grid(
            row=row, column=0, sticky="w", padx=10, pady=5)
        self.current_stock_var = tk.StringVar(value="0")
        ttk.Entry(scrollable_frame, textvariable=self.current_stock_var,
                  width=15).grid(row=row, column=1, sticky="w", padx=10, pady=5)
        row += 1

        # الحد الأدنى للمخزون
        ttk.Label(scrollable_frame, text="الحد الأدنى للمخزون:").grid(
            row=row, column=0, sticky="w", padx=10, pady=5)
        self.min_stock_var = tk.StringVar()
        ttk.Entry(scrollable_frame, textvariable=self.min_stock_var, width=15).grid(
            row=row, column=1, sticky="w", padx=10, pady=5)
        row += 1

        # نقطة إعادة الطلب
        ttk.Label(scrollable_frame, text="نقطة إعادة الطلب:").grid(
            row=row, column=0, sticky="w", padx=10, pady=5)
        self.reorder_point_var = tk.StringVar()
        ttk.Entry(scrollable_frame, textvariable=self.reorder_point_var, width=15).grid(
            row=row, column=1, sticky="w", padx=10, pady=5)
        row += 1

        # كمية إعادة الطلب
        ttk.Label(scrollable_frame, text="كمية إعادة الطلب:").grid(
            row=row, column=0, sticky="w", padx=10, pady=5)
        self.reorder_quantity_var = tk.StringVar()
        ttk.Entry(scrollable_frame, textvariable=self.reorder_quantity_var, width=15).grid(
            row=row, column=1, sticky="w", padx=10, pady=5)
        row += 1

        scrollable_frame.columnconfigure(1, weight=1)

    def create_additional_info_tab(self):
        """إنشاء تبويب المعلومات الإضافية"""
        self.additional_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.additional_frame, text="معلومات إضافية")

        # إنشاء Canvas للتمرير
        canvas = tk.Canvas(self.additional_frame)
        scrollbar = ttk.Scrollbar(self.additional_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        row = 0

        # معلومات المنتج
        ttk.Label(scrollable_frame, text="تفاصيل المنتج",
                 style='Title.TLabel').grid(row=row, column=0, columnspan=3, sticky="w", padx=10, pady=10)
        row += 1

        # العلامة التجارية
        ttk.Label(scrollable_frame, text="العلامة التجارية:").grid(
            row=row, column=0, sticky="w", padx=10, pady=5)
        self.brand_var = tk.StringVar()
        ttk.Entry(scrollable_frame, textvariable=self.brand_var, width=30).grid(
            row=row, column=1, columnspan=2, sticky="ew", padx=10, pady=5)
        row += 1

        # الموديل
        ttk.Label(scrollable_frame, text="الموديل:").grid(
            row=row, column=0, sticky="w", padx=10, pady=5)
        self.model_var = tk.StringVar()
        ttk.Entry(scrollable_frame, textvariable=self.model_var, width=30).grid(
            row=row, column=1, columnspan=2, sticky="ew", padx=10, pady=5)
        row += 1

        # اللون
        ttk.Label(scrollable_frame, text="اللون:").grid(
            row=row, column=0, sticky="w", padx=10, pady=5)
        self.color_var = tk.StringVar()
        ttk.Entry(scrollable_frame, textvariable=self.color_var, width=20).grid(
            row=row, column=1, sticky="ew", padx=10, pady=5)
        row += 1

        # الحجم
        ttk.Label(scrollable_frame, text="الحجم:").grid(
            row=row, column=0, sticky="w", padx=10, pady=5)
        self.size_var = tk.StringVar()
        ttk.Entry(scrollable_frame, textvariable=self.size_var, width=20).grid(
            row=row, column=1, sticky="ew", padx=10, pady=5)
        row += 1

        # الوزن
        ttk.Label(scrollable_frame, text="الوزن (كجم):").grid(
            row=row, column=0, sticky="w", padx=10, pady=5)
        self.weight_var = tk.StringVar()
        ttk.Entry(scrollable_frame, textvariable=self.weight_var, width=15).grid(
            row=row, column=1, sticky="w", padx=10, pady=5)
        row += 1

        scrollable_frame.columnconfigure(1, weight=1)

    def create_ai_tab(self):
        """إنشاء تبويب الذكاء الاصطناعي"""
        self.ai_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.ai_frame, text="ذكاء اصطناعي")

        # لوحة الاقتراحات
        suggestions_frame = ttk.LabelFrame(self.ai_frame, text="الاقتراحات الذكية")
        suggestions_frame.pack(fill=tk.X, padx=10, pady=5)

        # اقتراح التصنيف
        ttk.Button(suggestions_frame, text="🤖 اقتراح التصنيف",
                  command=self.suggest_category).pack(side=tk.LEFT, padx=5, pady=5)

        # توقع السعر
        ttk.Button(suggestions_frame, text="💰 توقع السعر",
                  command=self.predict_price).pack(side=tk.LEFT, padx=5, pady=5)

        # كشف التكرار
        ttk.Button(suggestions_frame, text="🔍 كشف التكرار",
                  command=self.detect_duplicates).pack(side=tk.LEFT, padx=5, pady=5)

        # تقييم الجودة
        ttk.Button(suggestions_frame, text="⭐ تقييم الجودة",
                  command=self.evaluate_quality).pack(side=tk.LEFT, padx=5, pady=5)

        # منطقة عرض النتائج
        self.ai_results_text = tk.Text(self.ai_frame, height=15, wrap=tk.WORD)
        self.ai_results_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # شريط تمرير للنتائج
        ai_scrollbar = ttk.Scrollbar(self.ai_frame, command=self.ai_results_text.yview)
        self.ai_results_text.config(yscrollcommand=ai_scrollbar.set)

    def create_analytics_tab(self):
        """إنشاء تبويب التحليلات"""
        self.analytics_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.analytics_frame, text="تحليلات")

        # أزرار التحليلات
        analytics_buttons_frame = ttk.LabelFrame(
            self.analytics_frame, text="التحليلات المتاحة"
        )
        analytics_buttons_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(analytics_buttons_frame, text="📈 اتجاه المبيعات",
                  command=self.show_sales_trend).pack(side=tk.LEFT, padx=5, pady=5)

        ttk.Button(analytics_buttons_frame, text="🥧 توزيع الفئات",
                  command=self.show_category_distribution).pack(
                      side=tk.LEFT, padx=5, pady=5)

        ttk.Button(analytics_buttons_frame, text="💹 تحليل الربحية",
                  command=self.show_profit_analysis).pack(
                      side=tk.LEFT, padx=5, pady=5)

        ttk.Button(analytics_buttons_frame, text="📦 حالة المخزون",
                  command=self.show_inventory_status).pack(
                      side=tk.LEFT, padx=5, pady=5)

        # منطقة عرض الرسوم البيانية
        self.analytics_display_frame = ttk.Frame(self.analytics_frame)
        self.analytics_display_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

    def create_preview_panel(self):
        """إنشاء لوحة المعاينة وقائمة الأصناف"""
        self.preview_frame = ttk.Frame(self.main_paned)
        self.main_paned.add(self.preview_frame, weight=1)

        # قائمة الأصناف
        items_frame = ttk.LabelFrame(self.preview_frame, text="قائمة الأصناف")
        items_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # إنشاء Treeview لعرض الأصناف
        columns = ('code', 'name', 'category', 'price', 'stock')
        self.items_tree = ttk.Treeview(
            items_frame, columns=columns, show='headings', height=15)

        # تعريف العناوين
        self.items_tree.heading('code', text='الرمز')
        self.items_tree.heading('name', text='الاسم')
        self.items_tree.heading('category', text='الفئة')
        self.items_tree.heading('price', text='السعر')
        self.items_tree.heading('stock', text='المخزون')

        # تعريف عرض الأعمدة
        self.items_tree.column('code', width=80)
        self.items_tree.column('name', width=200)
        self.items_tree.column('category', width=100)
        self.items_tree.column('price', width=80)
        self.items_tree.column('stock', width=80)

        # شريط التمرير للقائمة
        items_scrollbar = ttk.Scrollbar(
            items_frame, orient=tk.VERTICAL, command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=items_scrollbar.set)

        # تخطيط العناصر
        self.items_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        items_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # ربط الأحداث
        self.items_tree.bind('<Double-1>', self.on_item_double_click)
        self.items_tree.bind('<Button-3>', self.show_context_menu)

        # تحميل قائمة الأصناف
        self.load_items_list()

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_frame = ttk.Frame(self.window)
        self.status_frame.pack(fill=tk.X, side=tk.BOTTOM)

        # رسالة الحالة
        self.status_var = tk.StringVar()
        self.status_var.set("جاهز")
        self.status_label = ttk.Label(
            self.status_frame, textvariable=self.status_var)
        self.status_label.pack(side=tk.LEFT, padx=5, pady=2)

        # معلومات إضافية
        ttk.Separator(self.status_frame, orient=tk.VERTICAL).pack(
            side=tk.RIGHT, fill=tk.Y, padx=5)

        # عدد الأصناف
        self.items_count_var = tk.StringVar()
        self.items_count_var.set("الأصناف: 0")
        ttk.Label(self.status_frame, textvariable=self.items_count_var).pack(
            side=tk.RIGHT, padx=5, pady=2)

        # حالة قاعدة البيانات
        self.db_status_var = tk.StringVar()
        self.db_status_var.set("قاعدة البيانات: متصلة")
        ttk.Label(self.status_frame, textvariable=self.db_status_var,
                 style='Success.TLabel').pack(side=tk.RIGHT, padx=5, pady=2)

    def bind_events(self):
        """ربط الأحداث"""
        # أحداث النافذة
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)

        # أحداث لوحة المفاتيح
        self.window.bind('<Control-n>', lambda e: self.new_item())
        self.window.bind('<Control-s>', lambda e: self.save_item())
        self.window.bind('<Control-f>', lambda e: self.search_entry.focus())
        self.window.bind('<F1>', lambda e: self.show_user_guide())
        self.window.bind('<F5>', lambda e: self.refresh_data())

    def setup_keyboard_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        shortcuts = {
            '<Control-n>': self.new_item,
            '<Control-s>': self.save_item,
            '<Control-d>': self.delete_item,
            '<Control-f>': lambda: self.search_entry.focus(),
            '<Control-r>': self.refresh_data,
            '<F1>': self.show_user_guide,
            '<F5>': self.refresh_data,
            '<Escape>': self.clear_form
        }

        for key, command in shortcuts.items():
            self.window.bind(key, lambda e, cmd=command: cmd())

    # ==================== وظائف الأحداث ====================

    def on_name_change(self, event=None):
        """عند تغيير اسم الصنف"""
        if self.name_var.get() and not self.code_var.get():
            # توليد رمز تلقائي إذا لم يكن موجود
            self.generate_auto_code()

    def calculate_suggested_price(self, _event=None):
        """حساب السعر المقترح بناءً على سعر التكلفة"""
        try:
            cost_price = float(self.cost_price_var.get() or 0)
            if cost_price > 0:
                # حساب هامش ربح افتراضي 30%
                suggested_price = cost_price * 1.3
                if not self.selling_price_var.get():
                    self.selling_price_var.set(f"{suggested_price:.2f}")
                self.update_profit_margin()
        except ValueError:
            pass

    def update_profit_margin(self):
        """تحديث هامش الربح"""
        try:
            cost = float(self.cost_price_var.get() or 0)
            selling = float(self.selling_price_var.get() or 0)

            if cost > 0 and selling > 0:
                margin = ((selling - cost) / cost) * 100
                self.profit_margin_label.config(text=f"{margin:.1f}%")

                # تغيير اللون حسب الهامش
                if margin < 10:
                    self.profit_margin_label.config(style='Error.TLabel')
                elif margin < 25:
                    self.profit_margin_label.config(style='Warning.TLabel')
                else:
                    self.profit_margin_label.config(style='Success.TLabel')
            else:
                self.profit_margin_label.config(text="0%")
        except ValueError:
            self.profit_margin_label.config(text="خطأ")

    def on_search_change(self, event=None):
        """عند تغيير نص البحث"""
        search_text = self.search_var.get()
        if len(search_text) >= 2:  # البحث عند كتابة حرفين على الأقل
            self.perform_search()

    def on_item_double_click(self, event=None):
        """عند النقر المزدوج على صنف في القائمة"""
        selection = self.items_tree.selection()
        if selection:
            item_id = self.items_tree.item(selection[0])['values'][0]
            self.load_item_data(item_id)

    def on_closing(self):
        """عند إغلاق النافذة"""
        if self.has_unsaved_changes():
            result = messagebox.askyesnocancel(
                "حفظ التغييرات",
                "هناك تغييرات غير محفوظة. هل تريد حفظها قبل الإغلاق؟"
            )
            if result is True:  # نعم
                if self.save_item():
                    self.window.destroy()
            elif result is False:  # لا
                self.window.destroy()
            # إذا كان None (إلغاء) لا نفعل شيء
        else:
            self.window.destroy()

    # ==================== وظائف الأزرار ====================

    def new_item(self):
        """إنشاء صنف جديد"""
        if self.has_unsaved_changes():
            result = messagebox.askyesnocancel(
                "حفظ التغييرات",
                "هناك تغييرات غير محفوظة. هل تريد حفظها؟"
            )
            if result is True:
                if not self.save_item():
                    return
            elif result is None:
                return

        self.clear_form()
        self.update_status("تم إنشاء نموذج جديد")

    def save_item(self):
        """حفظ الصنف"""
        try:
            # التحقق من صحة البيانات
            if not self.validate_form():
                return False

            # جمع البيانات
            item_data = self.collect_form_data()

            # حفظ في قاعدة البيانات
            if self.save_to_database(item_data):
                self.update_status("تم حفظ الصنف بنجاح")
                self.load_items_list()  # تحديث القائمة
                return True
            else:
                return False

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ الصنف: {e}")
            return False

    def delete_item(self):
        """حذف الصنف المحدد"""
        selection = self.items_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد صنف للحذف")
            return

        item_code = self.items_tree.item(selection[0])['values'][0]

        result = messagebox.askyesno(
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف الصنف '{item_code}'؟"
        )

        if result:
            try:
                # حذف من قاعدة البيانات
                conn = self.database.get_connection()
                cursor = conn.cursor()
                cursor.execute(
                    "UPDATE items SET is_active = 0 WHERE code = ?", (item_code,))
                conn.commit()

                self.update_status(f"تم حذف الصنف '{item_code}' بنجاح")
                self.load_items_list()  # تحديث القائمة
                self.clear_form()

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف الصنف: {e}")

    def generate_auto_code(self):
        """توليد رمز تلقائي للصنف"""
        try:
            # الحصول على آخر رمز من قاعدة البيانات
            conn = self.database.get_connection()
            cursor = conn.cursor()
            cursor.execute(
                "SELECT MAX(CAST(SUBSTR(code, 5) AS INTEGER)) "
                "FROM items WHERE code LIKE 'ITM%'")
            result = cursor.fetchone()

            last_number = result[0] if result[0] else 0
            new_code = f"ITM{last_number + 1:05d}"

            self.code_var.set(new_code)

        except Exception as e:
            # في حالة الخطأ، استخدم timestamp
            import time
            timestamp = int(time.time())
            self.code_var.set(f"ITM{timestamp}")

    def clear_form(self):
        """مسح النموذج"""
        # مسح الحقول الأساسية
        self.name_var.set("")
        self.code_var.set("")
        self.category_var.set("")
        self.unit_var.set("")
        self.description_text.delete(1.0, tk.END)
        self.barcode_var.set("")

        # مسح حقول التسعير
        self.cost_price_var.set("")
        self.selling_price_var.set("")
        self.min_price_var.set("")
        self.max_price_var.set("")

        # مسح حقول المخزون
        self.current_stock_var.set("0")
        self.min_stock_var.set("")
        self.reorder_point_var.set("")
        self.reorder_quantity_var.set("")

        # مسح الحقول الإضافية
        self.brand_var.set("")
        self.model_var.set("")
        self.color_var.set("")
        self.size_var.set("")
        self.weight_var.set("")

        # مسح نتائج الذكاء الاصطناعي
        self.ai_results_text.delete(1.0, tk.END)

        # إعادة تعيين هامش الربح
        self.profit_margin_label.config(text="0%")

        self.current_item_data = {}

    # ==================== وظائف التحقق والحفظ ====================

    def validate_form(self):
        """التحقق من صحة البيانات"""
        errors = []

        # التحقق من الحقول المطلوبة
        if not self.name_var.get().strip():
            errors.append("اسم الصنف مطلوب")

        if not self.code_var.get().strip():
            errors.append("رمز الصنف مطلوب")

        if not self.category_var.get():
            errors.append("فئة الصنف مطلوبة")

        if not self.unit_var.get():
            errors.append("وحدة القياس مطلوبة")

        # التحقق من الأسعار
        try:
            cost_price = float(self.cost_price_var.get() or 0)
            if cost_price < 0:
                errors.append("سعر التكلفة لا يمكن أن يكون سالباً")
        except ValueError:
            errors.append("سعر التكلفة يجب أن يكون رقماً")

        try:
            selling_price = float(self.selling_price_var.get() or 0)
            if selling_price < 0:
                errors.append("سعر البيع لا يمكن أن يكون سالباً")
        except ValueError:
            errors.append("سعر البيع يجب أن يكون رقماً")

        # التحقق من المخزون
        try:
            current_stock = float(self.current_stock_var.get() or 0)
            if current_stock < 0:
                errors.append("المخزون الحالي لا يمكن أن يكون سالباً")
        except ValueError:
            errors.append("المخزون الحالي يجب أن يكون رقماً")

        # عرض الأخطاء إن وجدت
        if errors:
            messagebox.showerror("أخطاء في البيانات", "\n".join(errors))
            return False

        return True

    def collect_form_data(self):
        """جمع بيانات النموذج"""
        return {
            'uuid': str(uuid.uuid4()),
            'code': self.code_var.get().strip(),
            'name': self.name_var.get().strip(),
            'description': self.description_text.get(1.0, tk.END).strip(),
            'category': self.category_var.get(),
            'unit': self.unit_var.get(),
            'barcode': self.barcode_var.get().strip(),
            'cost_price': float(self.cost_price_var.get() or 0),
            'selling_price': float(self.selling_price_var.get() or 0),
            'min_price': float(self.min_price_var.get() or 0),
            'max_price': float(self.max_price_var.get() or 0),
            'current_stock': float(self.current_stock_var.get() or 0),
            'min_stock_level': float(self.min_stock_var.get() or 0),
            'reorder_point': float(self.reorder_point_var.get() or 0),
            'reorder_quantity': float(self.reorder_quantity_var.get() or 0),
            'brand': self.brand_var.get().strip(),
            'model': self.model_var.get().strip(),
            'color': self.color_var.get().strip(),
            'size': self.size_var.get().strip(),
            'weight': float(self.weight_var.get() or 0),
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            'is_active': True
        }

    def save_to_database(self, item_data):
        """حفظ البيانات في قاعدة البيانات"""
        try:
            conn = self.database.get_connection()
            cursor = conn.cursor()

            # التحقق من وجود الرمز
            cursor.execute(
                "SELECT id FROM items WHERE code = ? AND is_active = 1",
                (item_data['code'],))
            existing = cursor.fetchone()

            if existing and not self.current_item_data.get('id'):
                messagebox.showerror(
                    "خطأ", f"رمز الصنف '{item_data['code']}' موجود بالفعل")
                return False

            if self.current_item_data.get('id'):
                # تحديث صنف موجود
                self.update_existing_item(cursor, item_data)
            else:
                # إضافة صنف جديد
                self.insert_new_item(cursor, item_data)

            conn.commit()
            return True

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيانات: {e}")
            return False

    def insert_new_item(self, cursor, item_data):
        """إدراج صنف جديد"""
        sql = """
        INSERT INTO items (
            uuid, code, name, description, category_id, unit_id, barcode,
            cost_price, selling_price, min_price, max_price,
            current_stock, min_stock_level, reorder_point, reorder_quantity,
            brand, model, color, size, weight, created_at, updated_at, is_active
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
                  ?, ?)
        """

        # الحصول على معرفات الفئة والوحدة
        category_id = self.get_category_id(item_data['category'])
        unit_id = self.get_unit_id(item_data['unit'])

        cursor.execute(sql, (
            item_data['uuid'], item_data['code'], item_data['name'],
            item_data['description'],
            category_id, unit_id, item_data['barcode'],
            item_data['cost_price'], item_data['selling_price'],
            item_data['min_price'], item_data['max_price'],
            item_data['current_stock'], item_data['min_stock_level'],
            item_data['reorder_point'], item_data['reorder_quantity'],
            item_data['brand'], item_data['model'], item_data['color'],
            item_data['size'], item_data['weight'],
            item_data['created_at'], item_data['updated_at'], item_data['is_active']
        ))

    def get_category_id(self, category_name):
        """الحصول على معرف الفئة"""
        for category in self.categories_data:
            if category['name'] == category_name:
                return category['id']
        return None

    def get_unit_id(self, unit_name):
        """الحصول على معرف الوحدة"""
        for unit in self.units_data:
            if unit['name'] == unit_name:
                return unit['id']
        return None

    def has_unsaved_changes(self):
        """التحقق من وجود تغييرات غير محفوظة"""
        # مقارنة بسيطة - يمكن تحسينها لاحق
        return (self.name_var.get().strip() or
                self.code_var.get().strip() or
                self.cost_price_var.get().strip() or
                self.selling_price_var.get().strip())

    def load_items_list(self):
        """تحميل قائمة الأصناف"""
        try:
            # مسح القائمة الحالية
            for item in self.items_tree.get_children():
                self.items_tree.delete(item)

            # تحميل البيانات من قاعدة البيانات
            conn = self.database.get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                SELECT i.code, i.name, c.name as category, i.selling_price,
                       i.current_stock
                FROM items i
                LEFT JOIN categories c ON i.category_id = c.id
                WHERE i.is_active = 1
                ORDER BY i.name
            """)

            items = cursor.fetchall()

            # إضافة البيانات للقائمة
            for item in items:
                # التعامل مع sqlite3.Row objects
                code = item[0] if len(item) > 0 else ''
                name = item[1] if len(item) > 1 else ''
                category = item[2] if len(item) > 2 and item[2] is not None else ''
                selling_price = (item[3] if len(item) > 3 and item[3] is not None
                                else 0.0)
                current_stock = (item[4] if len(item) > 4 and item[4] is not None
                               else 0.0)

                self.items_tree.insert('', 'end', values=(
                    code,
                    name,
                    category,
                    f"{float(selling_price):.2f}",
                    f"{float(current_stock):.2f}"
                ))

            # تحديث عداد الأصناف
            if hasattr(self, 'items_count_var'):
                self.items_count_var.set(f"الأصناف: {len(items)}")

        except Exception as e:
            error_msg = f"فشل في تحميل قائمة الأصناف:\n{str(e)}"
            print(f"❌ خطأ في load_items_list: {e}")
            messagebox.showerror("خطأ في تحميل البيانات", error_msg)

            # إضافة رسالة في القائمة
            self.items_tree.insert('', 'end', values=(
                "خطأ", "فشل في تحميل البيانات", "تحقق من قاعدة البيانات",
                "0.00", "0.00"
            ))

    # ==================== وظائف الذكاء الاصطناعي ====================

    def suggest_category(self):
        """اقتراح تصنيف الصنف"""
        item_name = self.name_var.get().strip()
        description = self.description_text.get(1.0, tk.END).strip()

        if not item_name:
            messagebox.showwarning("تحذير", "يرجى إدخال اسم الصنف أولاً")
            return

        try:
            suggestion = self.ai_manager.suggest_category(item_name, description)

            result_text = f"🤖 اقتراح التصنيف:\n"
            result_text += f"الفئة المقترحة: {suggestion['suggested_category']}\n"
            result_text += f"مستوى الثقة: {suggestion['confidence']:.1%}\n"
            result_text += (f"الكلمات المطابقة: "
                           f"{', '.join(suggestion['matched_keywords'])}\n\n")

            self.ai_results_text.insert(tk.END, result_text)

            # تطبيق الاقتراح إذا كان مستوى الثقة عالي
            if suggestion['confidence'] > 0.7:
                self.category_var.set(suggestion['suggested_category'])

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في اقتراح التصنيف: {e}")

    def predict_price(self):
        """توقع سعر الصنف"""
        item_name = self.name_var.get().strip()
        category = self.category_var.get()
        cost_price = self.cost_price_var.get()

        if not item_name:
            messagebox.showwarning("تحذير", "يرجى إدخال اسم الصنف أولاً")
            return

        try:
            prediction = self.ai_manager.predict_price(
                item_name, category, cost_price)

            result_text = f"💰 توقع السعر:\n"
            result_text += (f"السعر المتوقع: "
                           f"{prediction['predicted_price']:.2f} ريال\n")
            result_text += (f"النطاق المقترح: "
                           f"{prediction['price_range']['min']:.2f} - "
                           f"{prediction['price_range']['max']:.2f} ريال\n")
            result_text += f"الطريقة المستخدمة: {prediction['method']}\n"
            result_text += f"مستوى الثقة: {prediction['confidence']:.1%}\n\n"

            self.ai_results_text.insert(tk.END, result_text)

            # تطبيق التوقع إذا لم يكن هناك سعر محدد
            if not self.selling_price_var.get():
                self.selling_price_var.set(f"{prediction['predicted_price']:.2f}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في توقع السعر: {e}")

    def detect_duplicates(self):
        """كشف الأصناف المكررة"""
        item_name = self.name_var.get().strip()

        if not item_name:
            messagebox.showwarning("تحذير", "يرجى إدخال اسم الصنف أولاً")
            return

        try:
            duplicates = self.ai_manager.detect_duplicates(item_name)

            result_text = f"🔍 كشف التكرار:\n"
            if duplicates['potential_duplicates']:
                result_text += (f"تم العثور على "
                               f"{len(duplicates['potential_duplicates'])} "
                               f"أصناف مشابهة:\n")
                for dup in duplicates['potential_duplicates']:
                    result_text += (f"- {dup['name']} "
                                   f"(تشابه: {dup['similarity']:.1%})\n")
            else:
                result_text += "لم يتم العثور على أصناف مشابهة\n"

            result_text += (f"مستوى التشابه الأدنى: "
                           f"{duplicates['similarity_threshold']:.1%}\n\n")

            self.ai_results_text.insert(tk.END, result_text)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في كشف التكرار: {e}")

    def evaluate_quality(self):
        """تقييم جودة بيانات الصنف"""
        try:
            item_data = self.collect_form_data()
            evaluation = self.ai_manager.evaluate_item_quality(item_data)

            result_text = f"⭐ تقييم الجودة:\n"
            result_text += f"النقاط الإجمالية: {evaluation['total_score']}/100\n"
            result_text += f"التقييم: {evaluation['quality_grade']}\n\n"

            result_text += "تفاصيل التقييم:\n"
            for criterion, score in evaluation['detailed_scores'].items():
                result_text += f"- {criterion}: {score}/20\n"

            if evaluation['suggestions']:
                result_text += "\nاقتراحات للتحسين:\n"
                for suggestion in evaluation['suggestions']:
                    result_text += f"• {suggestion}\n"

            result_text += "\n"
            self.ai_results_text.insert(tk.END, result_text)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تقييم الجودة: {e}")

    # ==================== وظائف التحليلات ====================

    def show_sales_trend(self):
        """عرض اتجاه المبيعات"""
        try:
            chart_data = self.analytics_engine.generate_sales_trend_chart()
            self.display_chart(chart_data, "اتجاه المبيعات")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في عرض اتجاه المبيعات: {e}")

    def show_category_distribution(self):
        """عرض توزيع الفئات"""
        try:
            chart_data = self.analytics_engine.generate_category_distribution_chart()
            self.display_chart(chart_data, "توزيع الفئات")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في عرض توزيع الفئات: {e}")

    def show_profit_analysis(self):
        """عرض تحليل الربحية"""
        try:
            chart_data = self.analytics_engine.generate_profit_analysis_chart()
            self.display_chart(chart_data, "تحليل الربحية")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في عرض تحليل الربحية: {e}")

    def show_inventory_status(self):
        """عرض حالة المخزون"""
        try:
            chart_data = self.analytics_engine.generate_inventory_status_chart()
            self.display_chart(chart_data, "حالة المخزون")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في عرض حالة المخزون: {e}")

    def display_chart(self, chart_data, title):
        """عرض الرسم البياني"""
        if chart_data['success']:
            # إنشاء نافذة جديدة للرسم البياني
            chart_window = tk.Toplevel(self.window)
            chart_window.title(f"تحليلات - {title}")
            chart_window.geometry("800x600")

            # عرض رسالة نجاح
            info_label = ttk.Label(chart_window, text=f"تم إنشاء {title} بنجاح")
            info_label.pack(pady=10)

            # عرض الإحصائيات
            stats_text = tk.Text(chart_window, height=10, wrap=tk.WORD)
            stats_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

            stats_content = f"📊 إحصائيات {title}:\n\n"
            for key, value in chart_data.get('statistics', {}).items():
                stats_content += f"{key}: {value}\n"

            stats_text.insert(tk.END, stats_content)

        else:
            messagebox.showerror(
                "خطأ", chart_data.get('error', 'فشل في إنشاء الرسم البياني'))

    # ==================== وظائف مساعدة ====================

    def update_status(self, message):
        """تحديث رسالة الحالة"""
        self.status_var.set(message)
        self.window.update_idletasks()

    def check_dependencies_async(self):
        """فحص التبعيات في الخلفية"""

        def check_deps():
            try:
                status = self.dependency_manager.check_all_dependencies()
                missing_deps = [dep for dep, info in status.items()
                               if not info['available']]

                if missing_deps:
                    self.window.after(
                        0, lambda: self.show_dependency_warning(missing_deps))
                else:
                    self.window.after(
                        0, lambda: self.update_status("جميع التبعيات متوفرة"))
            except Exception as e:
                print(f"خطأ في فحص التبعيات: {e}")

        thread = threading.Thread(target=check_deps, daemon=True)
        thread.start()

    def show_dependency_warning(self, missing_deps):
        """عرض تحذير التبعيات المفقودة"""
        message = f"تبعيات مفقودة: {', '.join(missing_deps)}\n"
        message += "بعض الميزات قد لا تعمل بشكل صحيح."
        messagebox.showwarning("تبعيات مفقودة", message)

    # وظائف إضافية (stubs للتطوير المستقبلي)
    def add_new_category(self): pass

    def add_new_unit(self): pass

    def generate_barcode(self): pass

    def perform_search(self): pass

    def load_item_data(self, item_id): pass

    def show_context_menu(self, event): pass

    def import_items(self): pass

    def export_items(self): pass

    def undo_action(self): pass

    def redo_action(self): pass

    def copy_item(self): pass

    def paste_item(self): pass

    def show_analytics_panel(self): pass

    def show_charts_panel(self): pass

    def show_user_guide(self):
        """عرض دليل المستخدم"""
        messagebox.showinfo("دليل المستخدم", "دليل المستخدم قيد التطوير...")

    def show_shortcuts(self):
        """عرض اختصارات لوحة المفاتيح"""
        shortcuts = """اختصارات لوحة المفاتيح:

Ctrl+N - صنف جديد
Ctrl+S - حفظ
Ctrl+D - حذف
F1 - مساعدة
Escape - إغلاق"""
        messagebox.showinfo("اختصارات لوحة المفاتيح", shortcuts)

    def show_about(self):
        """عرض معلومات البرنامج"""
        about_text = """نظام إدخال الأصناف الشامل والمتقدم

الإصدار: 1.0.0
المطور: فريق التطوير المتخصص
التاريخ: 2025-07-31

يتضمن:
• ذكاء اصطناعي متقدم
• تحليلات شاملة
• واجهة احترافية"""
        messagebox.showinfo("حول البرنامج", about_text)

    def refresh_data(self):
        """تحديث البيانات"""
        try:
            # إعادة تحميل قائمة الأصناف
            self.load_items_list()

            # إعادة تحميل قوائم الفئات والوحدات
            self.load_categories()
            self.load_units()

            messagebox.showinfo("تحديث", "تم تحديث البيانات بنجاح!")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحديث البيانات: {e}")




    def add_sample_data(self):
        """إضافة بيانات تجريبية للاختبار"""
        try:
            conn = self.database.get_connection()
            cursor = conn.cursor()

            # إضافة فئات تجريبية
            sample_categories = [
                ('ELEC', 'إلكترونيات', 'أجهزة إلكترونية متنوعة'),
                ('CLOTH', 'ملابس', 'ملابس وأزياء'),
                ('FOOD', 'أغذية', 'مواد غذائية'),
            ]

            for code, name, desc in sample_categories:
                cursor.execute("""
                    INSERT OR IGNORE INTO categories (code, name, description)
                    VALUES (?, ?, ?)
                """, (code, name, desc))

            # إضافة أصناف تجريبية
            sample_items = [
                ('LAPTOP001', 'لابتوب ديل', 1, 2500.00, 1800.00, 5.0),
                ('PHONE001', 'هاتف ذكي', 1, 1200.00, 900.00, 10.0),
                ('SHIRT001', 'قميص قطني', 2, 80.00, 50.00, 25.0),
                ('RICE001', 'أرز بسمتي', 3, 15.00, 12.00, 100.0),
            ]

            for code, name, cat_id, sell_price, cost_price, stock in sample_items:
                cursor.execute("""
                    INSERT OR IGNORE INTO items (
                        uuid, code, name, category_id, selling_price,
                        cost_price, current_stock, is_active
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, 1)
                """, (f"uuid-{code}", code, name, cat_id, sell_price,
                      cost_price, stock))

            conn.commit()
            messagebox.showinfo(
                "بيانات تجريبية", "تم إضافة البيانات التجريبية بنجاح!")

            # إعادة تحميل البيانات
            self.refresh_data()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إضافة البيانات التجريبية: {e}")

    def update_existing_item(self, cursor, item_data):
        """تحديث صنف موجود"""
        pass

    def close_window(self):
        """إغلاق النافذة"""
        if messagebox.askokcancel("إغلاق", "هل تريد إغلاق النافذة؟"):
            self.window.destroy()





def main():
    """تشغيل النافذة"""
    print("🚀 تشغيل نظام إدخال الأصناف الشامل والمتقدم...")

    try:
        app = AdvancedItemEntryComprehensive()
        app.window.mainloop()
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        messagebox.showerror("خطأ", f"فشل في تشغيل النظام: {e}")

if __name__ == "__main__":
    main()



